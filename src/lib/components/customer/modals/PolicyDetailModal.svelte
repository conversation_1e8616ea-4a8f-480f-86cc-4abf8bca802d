<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { t, language } from '$lib/stores/i18n';
	import { displayDate } from '$lib/utils';
	import { flyAndScale } from '$lib/utils';
	import { Button, Modal, Badge } from 'flowbite-svelte';
	import {
		ExclamationCircleOutline,
		BuildingOutline,
		CalendarMonthOutline,
		UserOutline,
		DollarOutline,
		InfoCircleOutline
	} from 'flowbite-svelte-icons';
	import { get } from 'svelte/store';

	const lang = get(language); // 'en' or 'th'

	// Constants
	const TABS = [
		{ id: 'policy-details', label: t('policy_details_tab'), key: 'policy_details' },
		{ id: 'claims-history', label: t('policy_claims_tab'), key: 'claims_history' }
	];

	// Props
	/** Whether the modal is open */
	export let isOpen = false;
	/** Selected policy object */
	export let selectedPolicy: any = null;
	/** Raw policy details data from the TPA API workflow */
	export let selectedPolicyDetails: any = null;

	// Event dispatcher
	const dispatch = createEventDispatcher<{
		close: void;
	}>();

	// Tab state
	let activeTab = 'policy-details';

	// Debug logging for props
	$: {
		console.log('PolicyDetailModal props changed:', {
			isOpen,
			hasSelectedPolicyDetails: !!selectedPolicyDetails,
			selectedPolicy: selectedPolicy,
			selectedPolicyDetails: selectedPolicyDetails,
			policyDetailsCount: selectedPolicyDetails?.policy_details_data?.ListOfPolDet?.length || 0,
			claimsCount: selectedPolicyDetails?.policy_details_data?.ListOfPolClaim?.length || 0
		});
	}

	// Modal transition parameters for smooth animations
	const modalTransitionParams = {
		y: -8,
		x: 0,
		start: 0.95,
		duration: 250
	};

	// Helper functions to extract data from raw API response
	function getClaims() {
		return selectedPolicyDetails?.policy_details_data?.ListOfPolClaim || [];
	}

	// Format date from DD/MM/YYYY format to display format
	function formatClaimDate(dateString: string): string {
		if (!dateString || dateString.trim() === '') {
			return '-';
		}

		try {
			// Handle DD/MM/YYYY format from TPA API
			const [day, month, year] = dateString.split('/');
			const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));

			if (isNaN(date.getTime())) {
				return dateString; // Return original if parsing fails
			}

			// Use existing displayDate utility for consistent formatting
			const { date: formattedDate } = displayDate(date.toISOString());
			return formattedDate;
		} catch {
			return dateString; // Return original if any error occurs
		}
	}

	function getMemberInfo() {
		// Extract member info from the first claim or policy data
		const claims = getClaims();
		if (claims.length > 0) {
			return {
				name: claims[0].ClmMemberName || '-',
				code: selectedPolicyDetails.member_code || '-',
				policyNo: claims[0].ClmPolNo || '-'
			};
		}
		return {
			name: '-',
			code: selectedPolicyDetails?.member_code || '-',
			policyNo: '-'
		};
	}

	// Helper function to identify if a coverage item is a remark
	function isRemarkCoverage(coverage: any): boolean {
		if (!coverage.CovNo || coverage.CovNo.trim() === '') {
			return true;
		}
		return coverage.CovNo === 'Remarks' || coverage.CovNo === 'หมายเหตุ';
	}

	// Functions
	function closeModal() {
		console.log('PolicyDetailModal closing...');
		isOpen = false;
		dispatch('close');
		console.log('PolicyDetailModal close event dispatched');
	}

	// Parse policy date from DD/MM/YYYY format to Date object
	function parsePolicyDate(dateString: string): Date | null {
		if (!dateString || typeof dateString !== 'string' || dateString.trim() === '') {
			return null;
		}

		const trimmed = dateString.trim();
		const parts = trimmed.split('/');

		if (parts.length !== 3) {
			return null;
		}

		const day = parseInt(parts[0], 10);
		const month = parseInt(parts[1], 10);
		const year = parseInt(parts[2], 10);

		// Validate date components
		if (
			isNaN(day) ||
			isNaN(month) ||
			isNaN(year) ||
			day < 1 ||
			day > 31 ||
			month < 1 ||
			month > 12 ||
			year < 1900
		) {
			return null;
		}

		// Create date object (month is 0-indexed in JavaScript)
		const date = new Date(year, month - 1, day);

		// Verify the date is valid (handles cases like 31/02/2024)
		if (date.getFullYear() !== year || date.getMonth() !== month - 1 || date.getDate() !== day) {
			return null;
		}

		return date;
	}

	// Get display status for UI
	function getDisplayStatus(effFrom: string, effTo: string): string {
		const startDate = parsePolicyDate(effFrom);
		const endDate = parsePolicyDate(effTo);
		const currentDate = new Date();

		// Set current date to start of day for accurate comparison
		currentDate.setHours(0, 0, 0, 0);

		// If either date is invalid, return inactive
		if (!startDate || !endDate) {
			return 'inactive';
		}

		// Set dates to start of day for accurate comparison
		startDate.setHours(0, 0, 0, 0);
		endDate.setHours(0, 0, 0, 0);

		// Check if policy hasn't started yet (pending/waiting period)
		if (currentDate < startDate) {
			return 'pending';
		}

		// Check if policy has expired
		if (currentDate > endDate) {
			return 'expired';
		}

		// Check if policy is nearly expired (within 30 days)
		const thirtyDaysFromNow = new Date(currentDate);
		thirtyDaysFromNow.setDate(currentDate.getDate() + 30);

		if (thirtyDaysFromNow > endDate) {
			return 'nearly_expired';
		}

		// Policy is currently active
		return 'active';
	}

	// Status color classes for badges with comprehensive status support
	type StatusKey =
		| 'active'
		| 'inactive'
		| 'nearly_expired'
		| 'expired'
		| 'pending'
		| 'cancelled'
		| 'suspended'
		| 'waiting_period';
	const statusColors: Record<StatusKey, string> & Record<string, string> = {
		active: 'bg-green-100 text-green-500 border-green-200',
		pending: 'bg-blue-100 text-blue-500 border-blue-200',
		nearly_expired: 'bg-yellow-100 text-yellow-500 border-yellow-200',
		expired: 'bg-red-100 text-red-500 border-red-200',
		inactive: 'bg-gray-100 text-gray-500 border-gray-200',
		cancelled: 'bg-yellow-100 text-yellow-500 border-yellow-200',
		suspended: 'bg-orange-100 text-orange-500 border-orange-200',
		waiting_period: 'bg-yellow-100 text-yellow-600 border-yellow-200'
	};

	// Get status color class
	function getStatusColorClass(status: string): string {
		const statusKey = status.toLowerCase() as StatusKey;
		return statusColors[statusKey] || statusColors.inactive;
	}
</script>

<!-- Policy Detail Modal -->
<Modal
	bind:open={isOpen}
	size="lg"
	autoclose={false}
	transition={flyAndScale}
	params={modalTransitionParams}
	class="h-[80vh] overflow-y-auto bg-gray-50"
>
	<div slot="header" class="flex flex-col gap-1 space-y-1">
		<div class="flex items-center justify-between">
			{#if selectedPolicy}
				{@const displayStatus = getDisplayStatus(selectedPolicy.EffFrom, selectedPolicy.EffTo)}
				<h2 class="flex items-center gap-3 text-lg font-semibold text-green-600">
					<!-- [{selectedPolicy.PolNo}] -->
					{selectedPolicy.PlanName}
					<Badge
						rounded
						border
						class="whitespace-nowrap text-xs {getStatusColorClass(displayStatus)}"
					>
						{t('policy_status_' + displayStatus)}
					</Badge>
				</h2>
			{/if}
		</div>
		<!-- {#if selectedPolicyDetails}
				{@const memberInfo = getMemberInfo()}
				<div class="flex flex-col gap-1 text-sm text-gray-600 sm:flex-row sm:gap-4">
					<span
						><strong>
							{t('policy_holder')}
						</strong>
						{memberInfo.name}</span
					>
					<span
						><strong>
							{t('policy_member_code')}
						</strong>
						{memberInfo.code}</span
					>
					<span
						><strong>
							{t('policy_no')}
						</strong>
						{memberInfo.policyNo}</span
					>
				</div>
			{/if} -->
	</div>

	{#if selectedPolicyDetails}
		{@const policyDetails = selectedPolicyDetails.policy_details_data.ListOfPolDet || []}
		{@const claims = getClaims()}
		{@const memberInfo = getMemberInfo()}

		<div>
			<!-- Tab Navigation -->
			<div class="mb-4 flex-shrink-0 border-b border-gray-200 bg-gray-50">
				<div class="scrollbar-hide overflow-x-auto">
					<nav class="flex min-w-max">
						{#each TABS as tab}
							<button
								on:click={() => {
									activeTab = tab.id;
								}}
								class="min-w-fit flex-1 whitespace-nowrap border-b-2 px-4 py-4 text-center text-sm font-medium transition-colors
									{activeTab === tab.id
									? 'border-black text-black'
									: 'border-transparent text-gray-500 hover:text-gray-700'}"
								role="tab"
								aria-selected={activeTab === tab.id}
								aria-controls="tab-content-{tab.id}"
							>
								{tab.label}
								{#if tab.id === 'claims-history'}
									<span
										class="ml-1 rounded-full bg-gray-200 px-2 py-1 text-xs font-medium text-gray-600"
									>
										{claims.length}
									</span>
								{/if}
							</button>
						{/each}
					</nav>
				</div>
			</div>

			<!-- Tab Content -->
			<div class="w-full">
				{#each TABS as tab}
					{#if activeTab === tab.id}
						<div
							role="tabpanel"
							aria-labelledby="tab-{tab.id}"
							id="tab-content-{tab.id}"
							class="overflow-y-auto"
						>
							{#if tab.id === 'policy-details'}
								<!-- Policy Details Tab Content -->

								<div class="space-y-4">
									<!-- Policy Information Section -->
									<!-- <div class="modal-content-section rounded-lg border border-gray-200 bg-white p-4">
										<h3 class="mb-3 text-lg font-semibold text-gray-700">
											{t('policy_modal_info_title')}
										</h3>
										<div class="grid grid-cols-1 gap-3 sm:grid-cols-2">
											<div
												class="flex flex-col items-center space-y-1 text-sm sm:flex-row sm:justify-start sm:space-y-0"
											>
												<span class="w-28 text-gray-600">{t('policy_no')}</span>
												<span class="font-semibold text-gray-900">{memberInfo.policyNo}</span>
											</div>
											<div
												class="flex flex-col items-center space-y-1 text-sm sm:flex-row sm:justify-start sm:space-y-0"
											>
												<span class="w-28 text-gray-600">{t('policy_member_code')}</span>
												<span class="font-semibold text-gray-900">{memberInfo.code}</span>
											</div>
											{#if claims.length > 0}
												<div
													class="flex flex-col items-center space-y-1 text-sm sm:flex-row sm:justify-start sm:space-y-0"
												>
													<span class="w-28 text-gray-600">{t('policy_insurer_name')}</span>
													<span class="font-semibold text-gray-900">
														{lang === 'th'? claims[0].ClmInsurerTH : claims[0].ClmInsurerEN}
													</span>
												</div>
												<div
													class="flex flex-col items-center space-y-1 text-sm sm:flex-row sm:justify-start sm:space-y-0"
												>
													<span class="w-28 text-gray-600">{t('policy_member_type')}</span>
													<span class="font-semibold text-gray-900">{claims[0].ClmCardType}</span>
												</div>
											{/if}
										</div>
									</div> -->

									<!-- Benefits Coverage Section -->
									{#if policyDetails.length > 0}
										<div
											class="modal-content-section rounded-lg border border-gray-200 bg-white p-4"
										>
											<div class="mb-3 flex items-center justify-between">
												<h3 class="text-lg font-semibold text-gray-900">
													{t('policy_benefit_section')}
												</h3>
												<!-- <span class="text-sm text-gray-600">(หน่วย: บาท)</span> -->
											</div>

											<div class="space-y-4">
												{#each policyDetails as benefit}
													<div class="rounded-lg border border-gray-200 p-4 shadow-sm">
														<h4 class="text-md mb-3 font-semibold text-gray-900">
															{lang === 'th'? benefit.MainBenefit : benefit.MainBenefitEN}
														</h4>

														{#if benefit.Coverage && benefit.Coverage.length > 0}
															{@const regularCoverage = benefit.Coverage.filter(
																(c) => c.CovDesc && c.CovDesc.trim() && !isRemarkCoverage(c)
															)}
															{@const remarksCoverage = benefit.Coverage.filter(
																(c) => c.CovDesc && c.CovDesc.trim() && isRemarkCoverage(c)
															)}

															<!-- Regular Coverage Items -->
															{#if regularCoverage.length > 0}
																<div class="space-y-2">
																	{#each regularCoverage as coverage}
																		<div
																			class="flex flex-col gap-2 rounded-lg bg-gray-50 p-3 text-sm sm:flex-row sm:justify-between"
																		>
																			<div class="flex-1">
																				<div class="text-gray-900">
																					{coverage.CovNo || coverage.CovNoEN}. {lang === 'th'? coverage.CovDesc : coverage.CovDescEN}
																				</div>
																			</div>
																			{#if coverage.CovLimit && coverage.CovLimit.trim()}
																				<div class="text-right">
																					<div class="font-bold text-gray-600">
																						{lang === 'th'? coverage.CovLimit : coverage.CovLimitEN}
																						{t('policy_amount_unit_baht')}
																					</div>
																					{#if coverage.CovUtilized && coverage.CovUtilized.trim() && coverage.CovUtilized !== '-'}
																						<div class="text-xs text-gray-600">
																							{t('policy_coverage_used')}
																							{lang === 'th'? coverage.CovUtilized : coverage.CovUtilizedEN}
																						</div>
																					{/if}
																				</div>
																			{/if}
																		</div>
																	{/each}
																</div>
															{/if}

															<!-- Consolidated Remarks Section -->
															{#if remarksCoverage.length > 0}
																<div
																	class="mt-4 rounded-lg border border-amber-200 bg-amber-50 p-4 text-sm"
																>
																	<div class="mb-3 flex items-center gap-2">
																		<ExclamationCircleOutline class="h-5 w-5 text-amber-600" />
																		<h5 class="font-semibold text-amber-800">
																			{t('policy_coverage_remarks')}
																		</h5>
																	</div>
																	<div class="space-y-1">
																		{#each remarksCoverage as remark}
																			<div class="">
																				<div class="text-gray-900">
																					{lang === 'th'? remark.CovDesc : remark.CovDescEN}
																				</div>
																			</div>
																		{/each}
																	</div>
																</div>
															{/if}
														{/if}
													</div>
												{/each}
											</div>
										</div>
									{/if}
								</div>
							{:else if tab.id === 'claims-history'}
								<!-- Claims History Tab Content -->

								<div class="space-y-4">
									{#if claims.length === 0}
										<div class="flex flex-col items-center justify-center py-12 text-center">
											<h3 class="mb-2 text-sm font-medium text-gray-900">
												{t('policy_claims_empty')}
											</h3>
											<!-- <p class="text-sm text-gray-500">ยังไม่มีการยื่นเคลมสำหรับกรมธรรม์นี้</p> -->
										</div>
									{:else}
										<!-- Claims Cards -->
										<div class="space-y-4">
											{#each claims as claim}
												<!-- Individual Claim Card -->
												<div
													class="modal-content-section rounded-lg border border-gray-200 bg-white p-4"
												>
													<!-- Header Section -->
													<div class="mb-4 flex items-center justify-between">
														<div class="flex items-center gap-3">
															<h3 class="text-md font-bold text-gray-900">
																{t('policy_claims_no')}
																{claim.ClmNo}
															</h3>
														</div>
														<Badge
															rounded
															color={claim.ClmStatus === 'Approved' || claim.ClmStatus === 'Authorized' || claim.ClmStatus === 'Paid' || claim.ClmStatus === 'Closed'
																? 'green'
																: claim.ClmStatus === 'Rejected' || claim.ClmStatus === 'Denied'
																	? 'red'
																	: 'yellow'}
															class="{claim.ClmStatus === 'Approved' || claim.ClmStatus === 'Authorized' || claim.ClmStatus === 'Paid' || claim.ClmStatus === 'Closed'
																? 'bg-green-200 text-green-800'
																: claim.ClmStatus === 'Rejected' || claim.ClmStatus === 'Denied'
																	? 'bg-red-200 text-red-800'
																	: 'bg-yellow-200 text-yellow-800'} px-3 py-1 text-xs font-medium"
														>
															{lang === 'th' ? claim.ClmStatusTxt : claim.ClmStatusTxtEN}
														</Badge>
													</div>

													<!-- Main Information Grid -->
													<div class="mb-4 grid grid-cols-2 gap-6">
														<!-- Provider -->
														<div class="flex items-start gap-3">
															<!-- <div class="rounded-lg bg-gray-100 p-2">
																<BuildingOutline class="h-5 w-5 text-gray-600" />
															</div> -->
															<div>
																<h4 class="text-sm text-gray-600">
																	{t('policy_claims_provider')}
																</h4>
																<p class="text-sm text-gray-900 font-medium">
																	{lang === 'th' ? claim.ClmProviderTH : claim.ClmProviderEN}
																</p>
															</div>
														</div>

														<!-- Claim Type -->
														<div class="flex items-start gap-3">
															<!-- <div class="rounded-lg bg-gray-100 p-2">
																<UserOutline class="h-5 w-5 text-gray-600" />
															</div> -->
															<div>
																<h4 class="text-sm text-gray-600">
																	{t('policy_claims_type')}
																</h4>
																<p class="text-sm text-gray-900 font-medium">{claim.ClmType}</p>
															</div>
														</div>

														<!-- Visit Date -->
														<div class="flex items-start gap-3">
															<!-- <div class="rounded-lg bg-gray-100 p-2">
																<CalendarMonthOutline class="h-5 w-5 text-gray-600" />
															</div> -->
															<div>
																<h4 class="text-sm text-gray-600">
																	{t('policy_claims_date_visit')}
																</h4>
																<p class="text-sm text-gray-900 font-medium">
																	{formatClaimDate(claim.ClmVisitDate)}
																</p>
															</div>
														</div>

														<!-- Discharge Date -->
														<div class="flex items-start gap-3">
															<!-- <div class="rounded-lg bg-gray-100 p-2">
																<CalendarMonthOutline class="h-5 w-5 text-gray-600" />
															</div> -->
															<div>
																<h4 class="text-sm text-gray-600">
																	{t('policy_claims_date_discharge')}
																</h4>
																<p class="text-sm text-gray-900 font-medium">
																	{formatClaimDate(claim.ClmDischargeDate)}
																</p>
															</div>
														</div>
													</div>

													<!-- Financial Details -->
													<div class="mb-4 flex items-center gap-3">
														<!-- <div class="rounded-lg bg-gray-100 p-2">
															<DollarOutline class="h-5 w-5 text-gray-600" />
														</div> -->
														<div>
															<h4 class="text-sm text-gray-600">
																{t('policy_claims_amount')}
															</h4>
															<div class="mt-1 flex items-baseline gap-4">
																<div class="flex items-baseline gap-2">
																	<span class="text-xs tracking-wide text-gray-600"
																		>{t('policy_claims_amount_incurred')}</span
																	>
																	<span class="text-sm font-semibold text-gray-900">
																		{claim.ClmIncurredAmt
																			? Number(claim.ClmIncurredAmt).toLocaleString()
																			: '-'}
																		{t('policy_amount_unit_baht')}
																	</span>
																</div>
																<div class="flex items-baseline gap-2">
																	<span class="text-xs tracking-wide text-gray-600"
																		>{t('policy_claims_amount_payable')}</span
																	>
																	<span class="text-sm font-semibold text-gray-900">
																		{claim.ClmPayable
																			? Number(claim.ClmPayable).toLocaleString()
																			: '-'}
																		{t('policy_amount_unit_baht')}
																	</span>
																</div>
																<div class="flex items-baseline gap-2">
																	<span class="text-xs tracking-wide text-gray-600"
																		>{t('policy_claims_source')}</span
																	>
																	<span class="text-xs font-semibold text-gray-700"
																		>{claim.ClmSource || '-'}</span
																	>
																</div>
																<div class="flex items-baseline gap-2">
																	<span class="text-xs tracking-wide text-gray-600"
																		>{t('policy_claims_date_settlement')}</span
																	>
																	<span class="text-xs font-semibold text-gray-700"
																		>{claim.ClmPaymentDate || '-'}</span
																	>
																</div>
															</div>
														</div>
													</div>

													<!-- Analysis Section -->
													<div class="mb-4 flex items-start gap-3">
														<!-- <div class="rounded-lg bg-gray-100 p-2">
															<InfoCircleOutline class="h-5 w-5 text-gray-600" />
														</div> -->
														<div>
															<h4 class="text-sm text-gray-600">
																{t('policy_claims_diagnosis')}
															</h4>
															<p class="text-sm text-gray-900 font-medium">
																{(lang === 'th' ? claim.ClmDiagTH : claim.ClmDiagEN) || '-'}
															</p>
														</div>
													</div>

													<!-- Company Information -->
													<div class="flex items-center border-t border-gray-200 pt-4">
														<div class="flex items-center gap-2">
															<Badge color="dark" class="bg-gray-200 px-3 py-1 text-gray-700">
																{claim.ClmCardType}
															</Badge>
															<p class="text-sm text-gray-900 font-medium">
																{lang === 'th' ? claim.ClmCompanyTH : claim.ClmCompanyEN}
															</p>
														</div>
													</div>
												</div>
											{/each}
										</div>
									{/if}
								</div>
							{/if}
						</div>
					{/if}
				{/each}
			</div>
		</div>
	{/if}

	<svelte:fragment slot="footer">
		<div class="modal-footer-right">
			<Button color="light" class="ml-auto" on:click={closeModal}>
				{t('policy_modal_close')}
			</Button>
		</div>
	</svelte:fragment>
</Modal>

<style>
	/* Modal footer button alignment - target the specific modal footer */
	:global(.modal-footer-right) {
		display: flex !important;
		justify-content: flex-end !important;
		width: 100% !important;
	}

	/* Additional targeting for Flowbite Modal footer */
	:global([data-modal-target] .modal-footer-right),
	:global(.fixed .modal-footer-right) {
		display: flex !important;
		justify-content: flex-end !important;
		width: 100% !important;
		margin-left: auto !important;
	}

	/* Hide scrollbar for horizontal tab navigation */
	.scrollbar-hide {
		-ms-overflow-style: none; /* IE and Edge */
		scrollbar-width: none; /* Firefox */
	}

	.scrollbar-hide::-webkit-scrollbar {
		display: none; /* Chrome, Safari and Opera */
	}

	/* Smooth horizontal scrolling for tabs */
	.scrollbar-hide {
		scroll-behavior: smooth;
	}
</style>
